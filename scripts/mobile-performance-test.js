#!/usr/bin/env node

/**
 * 移动端性能测试脚本
 * 用于测试移动端优化效果
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 性能测试配置
const PERFORMANCE_CONFIG = {
  // 测试 URL
  testUrls: [
    'http://localhost:3000',
    'http://localhost:3000/en',
    'http://localhost:3000/zh',
  ],
  
  // 移动端设备模拟
  mobileDevices: [
    'iPhone 12',
    'Samsung Galaxy S21',
    'iPad',
  ],
  
  // 性能指标阈值
  thresholds: {
    fcp: 1800,  // First Contentful Paint < 1.8s
    lcp: 2500,  // Largest Contentful Paint < 2.5s
    fid: 100,   // First Input Delay < 100ms
    cls: 0.1,   // Cumulative Layout Shift < 0.1
    ttfb: 600,  // Time to First Byte < 600ms
  },
  
  // CSS 文件大小阈值
  cssThresholds: {
    critical: 14000,    // 关键 CSS < 14KB
    total: 50000,       // 总 CSS < 50KB
    blocking: 25000,    // 阻塞 CSS < 25KB
  }
};

/**
 * 检查构建输出中的 CSS 文件
 */
function analyzeCSSFiles() {
  console.log('🔍 分析 CSS 文件...');
  
  const buildDir = path.join(process.cwd(), '.next');
  const staticDir = path.join(buildDir, 'static', 'css');
  
  if (!fs.existsSync(staticDir)) {
    console.log('❌ 未找到 CSS 构建文件');
    return;
  }
  
  const cssFiles = fs.readdirSync(staticDir).filter(file => file.endsWith('.css'));
  let totalSize = 0;
  let criticalSize = 0;
  
  console.log('\n📊 CSS 文件分析:');
  cssFiles.forEach(file => {
    const filePath = path.join(staticDir, file);
    const stats = fs.statSync(filePath);
    const sizeKB = (stats.size / 1024).toFixed(2);
    
    totalSize += stats.size;
    
    // 假设包含 'app' 的是关键 CSS
    if (file.includes('app')) {
      criticalSize += stats.size;
    }
    
    console.log(`  📄 ${file}: ${sizeKB} KB`);
  });
  
  console.log(`\n📈 CSS 总计:`);
  console.log(`  总大小: ${(totalSize / 1024).toFixed(2)} KB`);
  console.log(`  关键 CSS: ${(criticalSize / 1024).toFixed(2)} KB`);
  
  // 检查阈值
  if (totalSize > PERFORMANCE_CONFIG.cssThresholds.total) {
    console.log(`⚠️  总 CSS 大小超过阈值 (${PERFORMANCE_CONFIG.cssThresholds.total / 1024}KB)`);
  } else {
    console.log(`✅ CSS 大小在合理范围内`);
  }
  
  if (criticalSize > PERFORMANCE_CONFIG.cssThresholds.critical) {
    console.log(`⚠️  关键 CSS 大小超过阈值 (${PERFORMANCE_CONFIG.cssThresholds.critical / 1024}KB)`);
  } else {
    console.log(`✅ 关键 CSS 大小合理`);
  }
}

/**
 * 检查阻塞渲染的资源
 */
function checkRenderBlockingResources() {
  console.log('\n🚫 检查阻塞渲染的资源...');
  
  // 检查 public 目录中的 CSS 文件
  const publicDir = path.join(process.cwd(), 'public');
  const stylesDir = path.join(publicDir, 'styles');
  
  if (fs.existsSync(stylesDir)) {
    const styleFiles = fs.readdirSync(stylesDir).filter(file => file.endsWith('.css'));
    
    styleFiles.forEach(file => {
      const filePath = path.join(stylesDir, file);
      const stats = fs.statSync(filePath);
      const sizeKB = (stats.size / 1024).toFixed(2);
      
      console.log(`  📄 ${file}: ${sizeKB} KB`);
      
      if (file.includes('critical')) {
        console.log(`    ✅ 关键 CSS - 应该内联`);
      } else {
        console.log(`    ⏳ 非关键 CSS - 应该延迟加载`);
      }
    });
  }
}

/**
 * 生成性能优化建议
 */
function generateOptimizationSuggestions() {
  console.log('\n💡 性能优化建议:');
  
  const suggestions = [
    '1. 确保关键 CSS 已内联到 HTML 中',
    '2. 非关键 CSS 使用 media="print" 延迟加载',
    '3. 使用 preload 预加载关键资源',
    '4. 启用字体 display: swap',
    '5. 压缩和优化图片',
    '6. 使用 Service Worker 缓存静态资源',
    '7. 启用 gzip/brotli 压缩',
    '8. 移动端特定的 CSS 优化',
  ];
  
  suggestions.forEach(suggestion => {
    console.log(`  ${suggestion}`);
  });
}

/**
 * 检查移动端优化
 */
function checkMobileOptimizations() {
  console.log('\n📱 检查移动端优化...');
  
  // 检查是否有移动端特定的 CSS
  const srcDir = path.join(process.cwd(), 'src');
  const componentsDir = path.join(srcDir, 'components');
  
  // 检查 MobileOptimizedLayout 组件
  const mobileLayoutPath = path.join(componentsDir, 'MobileOptimizedLayout.tsx');
  if (fs.existsSync(mobileLayoutPath)) {
    console.log('  ✅ 移动端优化布局组件已存在');
  } else {
    console.log('  ❌ 缺少移动端优化布局组件');
  }
  
  // 检查关键 CSS 提取器
  const libDir = path.join(srcDir, 'lib');
  const criticalCSSPath = path.join(libDir, 'critical-css-extractor.ts');
  if (fs.existsSync(criticalCSSPath)) {
    console.log('  ✅ 关键 CSS 提取器已存在');
  } else {
    console.log('  ❌ 缺少关键 CSS 提取器');
  }
  
  // 检查 CSS 优化器
  const cssOptimizerPath = path.join(libDir, 'css-optimizer.ts');
  if (fs.existsSync(cssOptimizerPath)) {
    console.log('  ✅ CSS 优化器已存在');
  } else {
    console.log('  ❌ 缺少 CSS 优化器');
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 移动端性能测试开始...\n');
  
  try {
    // 检查构建是否存在
    const buildDir = path.join(process.cwd(), '.next');
    if (!fs.existsSync(buildDir)) {
      console.log('❌ 未找到构建文件，请先运行 npm run build');
      process.exit(1);
    }
    
    // 执行各项检查
    analyzeCSSFiles();
    checkRenderBlockingResources();
    checkMobileOptimizations();
    generateOptimizationSuggestions();
    
    console.log('\n✅ 移动端性能测试完成!');
    console.log('\n📋 下一步:');
    console.log('  1. 运行 npm run dev 启动开发服务器');
    console.log('  2. 使用 Chrome DevTools 的 Lighthouse 测试移动端性能');
    console.log('  3. 检查 Network 面板中的资源加载顺序');
    console.log('  4. 验证关键 CSS 是否已内联');
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error.message);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  main();
}

module.exports = {
  analyzeCSSFiles,
  checkRenderBlockingResources,
  checkMobileOptimizations,
  generateOptimizationSuggestions,
};
