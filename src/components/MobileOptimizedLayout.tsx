'use client'

import { useEffect } from 'react'
import { generateInlineCriticalCSS, generateDeferredCSSLoader, MOBILE_PERFORMANCE_CONFIG } from '@/lib/critical-css-extractor'

interface MobileOptimizedLayoutProps {
  children: React.ReactNode
}

/**
 * 移动端优化布局组件
 * 实现关键 CSS 内联和非关键 CSS 延迟加载
 */
export function MobileOptimizedLayout({ children }: MobileOptimizedLayoutProps) {
  useEffect(() => {
    // 移动端性能优化初始化
    const initMobileOptimizations = () => {
      // 1. 内联关键 CSS（如果还没有）
      if (!document.getElementById('critical-css')) {
        const criticalStyle = document.createElement('style')
        criticalStyle.id = 'critical-css'
        criticalStyle.innerHTML = `
          /* 移动端关键 CSS */
          html { 
            scroll-behavior: smooth;
            -webkit-text-size-adjust: 100%;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
          }
          
          body { 
            margin: 0;
            padding: 0;
            font-family: var(--font-inter), system-ui, sans-serif;
            line-height: 1.6;
          }
          
          img, video { 
            max-width: 100%;
            height: auto;
            display: block;
          }
          
          .container { 
            width: 100%;
            max-width: 1280px;
            margin: 0 auto;
            padding: 0 1rem;
          }
          
          @media (max-width: 768px) {
            .container { padding: 0 0.75rem; }
          }
        `
        document.head.insertBefore(criticalStyle, document.head.firstChild)
      }

      // 2. 预加载关键资源
      MOBILE_PERFORMANCE_CONFIG.criticalResources.forEach(resource => {
        const link = document.createElement('link')
        link.rel = 'preload'
        link.as = 'image'
        link.href = resource
        document.head.appendChild(link)
      })

      // 3. 延迟加载非关键 CSS
      const loadNonCriticalCSS = () => {
        // 查找页面中的 CSS 文件
        const cssLinks = Array.from(document.querySelectorAll('link[rel="stylesheet"]'))
        
        cssLinks.forEach(link => {
          const href = (link as HTMLLinkElement).href
          
          // 如果是 Next.js 生成的 CSS 文件，延迟加载
          if (href.includes('/_next/static/css/')) {
            const newLink = document.createElement('link')
            newLink.rel = 'stylesheet'
            newLink.href = href
            newLink.media = 'print'
            newLink.onload = function() {
              (this as HTMLLinkElement).media = 'all'
            }
            
            // 移除原始链接，添加延迟加载的链接
            link.remove()
            document.head.appendChild(newLink)
          }
        })
      }

      // 4. 使用 requestIdleCallback 或 setTimeout 延迟执行
      if ('requestIdleCallback' in window) {
        requestIdleCallback(loadNonCriticalCSS, { timeout: 1000 })
      } else {
        setTimeout(loadNonCriticalCSS, 100)
      }

      // 5. 移动端特定优化
      if (window.innerWidth <= 768) {
        // 移动端图片懒加载优化
        const images = document.querySelectorAll('img[loading="lazy"]')
        images.forEach(img => {
          (img as HTMLImageElement).loading = 'lazy'
          if (!(img as HTMLImageElement).sizes) {
            (img as HTMLImageElement).sizes = MOBILE_PERFORMANCE_CONFIG.mobileImageSizes
          }
        })

        // 移动端字体优化
        const fontLinks = document.querySelectorAll('link[href*="fonts.googleapis.com"]')
        fontLinks.forEach(link => {
          (link as HTMLLinkElement).setAttribute('font-display', 'swap')
        })
      }
    }

    // 立即执行关键优化
    initMobileOptimizations()

    // 监听页面可见性变化
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // 页面重新可见时，确保优化已应用
        initMobileOptimizations()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [])

  return <>{children}</>
}

/**
 * 移动端性能监控组件
 */
export function MobilePerformanceMonitor() {
  useEffect(() => {
    // 监控移动端性能指标
    const monitorMobilePerformance = () => {
      if ('performance' in window && 'getEntriesByType' in performance) {
        // 监控 FCP (First Contentful Paint)
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.name === 'first-contentful-paint') {
              console.log(`Mobile FCP: ${entry.startTime}ms`)
              
              // 如果 FCP 超过 1.8s，记录警告
              if (entry.startTime > 1800) {
                console.warn('Mobile FCP is slow:', entry.startTime)
              }
            }
            
            if (entry.name === 'largest-contentful-paint') {
              console.log(`Mobile LCP: ${entry.startTime}ms`)
              
              // 如果 LCP 超过 2.5s，记录警告
              if (entry.startTime > 2500) {
                console.warn('Mobile LCP is slow:', entry.startTime)
              }
            }
          }
        })

        observer.observe({ entryTypes: ['paint', 'largest-contentful-paint'] })

        // 清理函数
        return () => observer.disconnect()
      }
    }

    const cleanup = monitorMobilePerformance()
    return cleanup
  }, [])

  return null
}

/**
 * 移动端资源预加载组件
 */
export function MobileResourcePreloader() {
  useEffect(() => {
    // 移动端特定的资源预加载
    const preloadMobileResources = () => {
      // 预加载关键图片
      MOBILE_PERFORMANCE_CONFIG.criticalResources.forEach(resource => {
        const img = new Image()
        img.src = resource
      })

      // 预连接到重要域名
      MOBILE_PERFORMANCE_CONFIG.preconnectDomains.forEach(domain => {
        const link = document.createElement('link')
        link.rel = 'preconnect'
        link.href = domain
        if (domain.includes('fonts')) {
          link.crossOrigin = 'anonymous'
        }
        document.head.appendChild(link)
      })
    }

    // 延迟执行预加载，避免阻塞首次渲染
    setTimeout(preloadMobileResources, 100)
  }, [])

  return null
}
