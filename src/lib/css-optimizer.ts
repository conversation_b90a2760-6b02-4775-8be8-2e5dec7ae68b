/**
 * CSS 优化工具
 * 动态加载和优化 CSS
 */

// 关键 CSS 内联
export const criticalCSS = `
  /* 关键路径 CSS - 首屏渲染必需 */
  html, body {
    margin: 0;
    padding: 0;
    font-family: var(--font-inter), system-ui, sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* 防止布局偏移的基础样式 */
  img, video {
    max-width: 100%;
    height: auto;
  }
  
  /* 加载状态样式 */
  .loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }
  
  @keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
  }
  
  /* 关键按钮样式 */
  .btn-primary {
    background: #0070f3;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .btn-primary:hover {
    background: #0051cc;
  }
`

// 非关键 CSS 延迟加载
export function loadNonCriticalCSS(): void {
  if (typeof window === 'undefined') return

  // 延迟加载非关键 CSS - 移除不存在的文件引用
  const loadCSS = (href: string) => {
    // 检查文件是否存在
    fetch(href, { method: 'HEAD' })
      .then(response => {
        if (response.ok) {
          const link = document.createElement('link')
          link.rel = 'stylesheet'
          link.href = href
          link.media = 'print'
          link.onload = () => {
            link.media = 'all'
          }
          document.head.appendChild(link)
        }
      })
      .catch(() => {
        // 文件不存在，忽略错误
      })
  }

  // 使用 requestIdleCallback 在空闲时加载
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      // 只加载存在的 CSS 文件
      loadCSS('/styles/non-critical.css')
    })
  } else {
    // 降级方案
    setTimeout(() => {
      loadCSS('/styles/non-critical.css')
    }, 100)
  }
}

// 简化的 CSS 优化 - 移除复杂的运行时 CSS 清理
// 注意：运行时移除 CSS 规则可能导致样式问题，建议使用构建时优化
export function removeUnusedCSS(): void {
  if (typeof window === 'undefined') return

  // 简化版本：只在开发环境中记录未使用的类
  if (process.env.NODE_ENV === 'development') {
    console.log('CSS optimization: Use build-time tools like PurgeCSS for production')
  }

  // 实际的 CSS 优化应该在构建时进行，而不是运行时
  // 建议使用 Tailwind CSS 的内置 purge 功能或 PurgeCSS
}

// CSS 压缩工具
export function compressCSS(css: string): string {
  return css
    // 移除注释
    .replace(/\/\*[\s\S]*?\*\//g, '')
    // 移除多余空白
    .replace(/\s+/g, ' ')
    // 移除分号前的空格
    .replace(/\s*;\s*/g, ';')
    // 移除大括号前后的空格
    .replace(/\s*{\s*/g, '{')
    .replace(/\s*}\s*/g, '}')
    // 移除冒号后的空格
    .replace(/:\s+/g, ':')
    // 移除逗号后的空格
    .replace(/,\s+/g, ',')
    // 移除开头和结尾的空格
    .trim()
}

// 内联关键 CSS
export function inlineCriticalCSS(): void {
  if (typeof document === 'undefined') return

  const style = document.createElement('style')
  style.textContent = compressCSS(criticalCSS)
  document.head.insertBefore(style, document.head.firstChild)
}

// 移动端 CSS 优化
export function optimizeMobileCSS(): void {
  if (typeof window === 'undefined') return

  const isMobile = window.innerWidth <= 768

  if (isMobile) {
    // 移除或延迟加载桌面端特定的 CSS
    const desktopOnlyStyles = document.querySelectorAll('link[rel="stylesheet"][media="screen and (min-width: 769px)"]')
    desktopOnlyStyles.forEach(link => {
      (link as HTMLLinkElement).disabled = true
    })

    // 优化移动端字体加载
    const fontLinks = document.querySelectorAll('link[href*="fonts.googleapis.com"]')
    fontLinks.forEach(link => {
      (link as HTMLLinkElement).setAttribute('font-display', 'swap')
    })

    // 添加移动端特定的关键 CSS
    const mobileStyle = document.createElement('style')
    mobileStyle.id = 'mobile-critical-css'
    mobileStyle.innerHTML = `
      /* 移动端关键 CSS */
      @media (max-width: 768px) {
        .container { padding: 0 0.75rem !important; }
        .hero-section { min-height: 80vh !important; padding: 1.5rem 0.75rem !important; }
        .btn-primary { padding: 0.625rem 1.25rem !important; font-size: 0.875rem !important; }

        /* 移动端性能优化 */
        * { -webkit-tap-highlight-color: transparent; }
        body { -webkit-text-size-adjust: 100%; }
        img { will-change: transform; }
      }
    `
    document.head.appendChild(mobileStyle)
  }
}

// 获取优化后的 CSS
export function getOptimizedCSS(): string {
  return compressCSS(criticalCSS)
}
