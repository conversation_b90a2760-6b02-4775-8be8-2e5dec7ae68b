/**
 * 关键 CSS 提取和优化工具
 * 用于移动端性能优化，减少阻塞渲染的资源
 */

// 关键 CSS 选择器 - 首屏必需的样式
const CRITICAL_CSS_SELECTORS = [
  // 基础布局
  'html', 'body', 'main',
  
  // 导航栏
  'header', 'nav', '.header', '.navigation',
  
  // 首屏内容
  '.hero', '.hero-section', '.landing-hero',
  '.hero-title', '.hero-description', '.hero-button',
  
  // 关键按钮和链接
  '.btn', '.btn-primary', '.btn-secondary',
  '.button', '.link',
  
  // 布局容器
  '.container', '.wrapper', '.max-w-container',
  '.grid', '.flex', '.flex-col', '.flex-row',
  
  // 响应式工具类
  '.hidden', '.block', '.inline', '.inline-block',
  '.w-full', '.h-full', '.min-h-screen',
  
  // 文字样式
  '.text-', '.font-', '.leading-', '.tracking-',
  
  // 间距
  '.p-', '.m-', '.px-', '.py-', '.mx-', '.my-',
  '.pt-', '.pb-', '.pl-', '.pr-',
  '.mt-', '.mb-', '.ml-', '.mr-',
  
  // 背景和边框
  '.bg-', '.border', '.rounded',
  
  // 阴影和效果
  '.shadow', '.opacity-',
  
  // 动画（关键的）
  '.transition', '.duration-', '.ease-',
]

// 移动端关键 CSS
const MOBILE_CRITICAL_CSS = `
/* 移动端关键 CSS - 内联到 HTML */
html {
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-inter), system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  line-height: 1.6;
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

/* 防止布局偏移 */
img, video {
  max-width: 100%;
  height: auto;
  display: block;
}

/* 关键布局 */
.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* 首屏英雄区域 */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem 1rem;
}

/* 关键按钮样式 */
.btn-primary {
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-primary:hover {
  opacity: 0.9;
}

/* 导航栏 */
header {
  position: sticky;
  top: 0;
  z-index: 50;
  background: hsl(var(--background) / 0.8);
  backdrop-filter: blur(8px);
  border-bottom: 1px solid hsl(var(--border));
}

/* 响应式工具类 */
.hidden { display: none; }
.block { display: block; }
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.text-center { text-align: center; }
.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }

/* 移动端优化 */
@media (max-width: 768px) {
  .container {
    padding: 0 0.75rem;
  }
  
  .hero-section {
    padding: 1.5rem 0.75rem;
    min-height: 80vh;
  }
  
  .btn-primary {
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
  }
}
`

/**
 * 获取关键 CSS 内容
 */
export function getCriticalCSS(): string {
  return MOBILE_CRITICAL_CSS
}

/**
 * 检查是否为关键 CSS 选择器
 */
export function isCriticalSelector(selector: string): boolean {
  return CRITICAL_CSS_SELECTORS.some(critical => 
    selector.includes(critical) || selector.startsWith(critical)
  )
}

/**
 * 生成内联关键 CSS 的 HTML
 */
export function generateInlineCriticalCSS(): string {
  return `<style id="critical-css">${getCriticalCSS()}</style>`
}

/**
 * 生成非关键 CSS 的延迟加载脚本
 */
export function generateDeferredCSSLoader(cssFiles: string[]): string {
  return `
<script>
(function() {
  // 延迟加载非关键 CSS
  function loadCSS(href) {
    var link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;
    link.media = 'print';
    link.onload = function() {
      this.media = 'all';
    };
    document.head.appendChild(link);
  }
  
  // 在页面加载完成后加载非关键 CSS
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      ${cssFiles.map(file => `loadCSS('${file}');`).join('\n      ')}
    });
  } else {
    ${cssFiles.map(file => `loadCSS('${file}');`).join('\n    ')}
  }
})();
</script>`
}

/**
 * 移动端性能优化配置
 */
export const MOBILE_PERFORMANCE_CONFIG = {
  // 关键资源预加载
  criticalResources: [
    '/avatars/01.jpeg',
    '/avatars/02.jpeg', 
    '/avatars/03.jpeg',
  ],
  
  // 非关键 CSS 文件
  deferredCSS: [
    '/_next/static/css/app/layout.css',
    '/_next/static/css/app/globals.css',
  ],
  
  // 移动端图片优化
  mobileImageSizes: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  
  // 移动端字体优化
  fontDisplay: 'swap',
  
  // 预连接域名
  preconnectDomains: [
    'https://fonts.googleapis.com',
    'https://fonts.gstatic.com',
  ],
}
